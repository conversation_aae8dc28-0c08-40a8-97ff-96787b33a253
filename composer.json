{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "https://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "https://www.yiiframework.com/forum/", "wiki": "https://www.yiiframework.com/wiki/", "irc": "ircs://irc.libera.chat:6697/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=8.2.0", "yiisoft/yii2": "~2.0.45", "yiisoft/yii2-bootstrap5": "~2.0.2", "yiisoft/yii2-symfonymailer": "~2.0.3", "hail812/yii2-adminlte3": "^1.1"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-faker": "~2.0.0", "phpunit/phpunit": "~9.5.0", "codeception/codeception": "^5.0.0 || ^4.0", "codeception/lib-innerbrowser": "^4.0 || ^3.0 || ^1.1", "codeception/module-asserts": "^3.0 || ^1.1", "codeception/module-yii2": "^1.1", "codeception/module-filesystem": "^3.0 || ^2.0 || ^1.1", "codeception/verify": "^3.0 || ^2.2"}, "autoload-dev": {"psr-4": {"common\\tests\\": ["common/tests/", "common/tests/_support"], "backend\\tests\\": ["backend/tests/", "backend/tests/_support"], "frontend\\tests\\": ["frontend/tests/", "frontend/tests/_support"]}}, "config": {"allow-plugins": {"yiisoft/yii2-composer": true}, "process-timeout": 1800, "fxp-asset": {"enabled": false}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}