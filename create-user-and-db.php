<?php
// Create user and database using root access
echo "Creating user 'ssl' and database 'hmis'...\n";

// Try different root connection methods
$root_connections = [
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
];

$root_pdo = null;

foreach ($root_connections as $conn) {
    try {
        $dsn = "mysql:host={$conn['host']};port={$conn['port']}";
        echo "Trying root connection: {$dsn} with password: " . ($conn['pass'] ? 'YES' : 'NO') . "\n";
        
        $root_pdo = new PDO($dsn, $conn['user'], $conn['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "✅ Root connection successful!\n";
        break;
        
    } catch (PDOException $e) {
        echo "❌ Failed: " . $e->getMessage() . "\n";
        continue;
    }
}

if (!$root_pdo) {
    echo "❌ Could not connect as root. Cannot create user and database.\n";
    echo "Please ensure MySQL is running and root access is available.\n";
    exit(1);
}

try {
    // Create database
    echo "\n📊 Creating database 'hmis'...\n";
    $root_pdo->exec("CREATE DATABASE IF NOT EXISTS hmis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'hmis' created/verified!\n";
    
    // Check if user exists
    echo "\n👤 Checking if user 'ssl' exists...\n";
    $stmt = $root_pdo->prepare("SELECT COUNT(*) FROM mysql.user WHERE User = ? AND Host = ?");
    $stmt->execute(['ssl', 'localhost']);
    $user_exists = $stmt->fetchColumn() > 0;
    
    if ($user_exists) {
        echo "✅ User 'ssl'@'localhost' already exists!\n";
    } else {
        echo "❌ User 'ssl'@'localhost' does not exist. Creating...\n";
        $root_pdo->exec("CREATE USER 'ssl'@'localhost' IDENTIFIED BY '@password..'");
        echo "✅ User 'ssl'@'localhost' created!\n";
    }
    
    // Grant privileges
    echo "\n🔐 Granting privileges to 'ssl'@'localhost' on 'hmis' database...\n";
    $root_pdo->exec("GRANT ALL PRIVILEGES ON hmis.* TO 'ssl'@'localhost'");
    $root_pdo->exec("FLUSH PRIVILEGES");
    echo "✅ Privileges granted!\n";
    
    // Test the ssl user connection
    echo "\n🔍 Testing 'ssl' user connection...\n";
    $test_dsn = "mysql:host=localhost;port=3306;dbname=hmis";
    $test_pdo = new PDO($test_dsn, 'ssl', '@password..', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ User 'ssl' can connect to 'hmis' database successfully!\n";
    
    // Show tables
    $stmt = $test_pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "\n📋 Tables in 'hmis' database: " . count($tables) . "\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Setup completed successfully!\n";
?>
