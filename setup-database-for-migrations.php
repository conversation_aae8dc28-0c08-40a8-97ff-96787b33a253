<?php
// Setup database for migrations with ssl user
echo "=== Setting up database for migrations ===\n";

// Try to connect as root to create database and user
$root_connections = [
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
];

$root_pdo = null;
$working_connection = null;

foreach ($root_connections as $conn) {
    try {
        $dsn = "mysql:host={$conn['host']};port={$conn['port']}";
        echo "Trying root connection: {$dsn} with user '{$conn['user']}'" . 
             ($conn['pass'] ? " (with password)" : " (no password)") . "\n";
        
        $root_pdo = new PDO($dsn, $conn['user'], $conn['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "✅ Root connection successful!\n";
        $working_connection = $conn;
        break;
        
    } catch (PDOException $e) {
        echo "❌ Failed: " . $e->getMessage() . "\n";
    }
}

if (!$root_pdo) {
    echo "\n❌ Cannot connect as root. Trying alternative approach...\n";
    
    // Try to connect directly as ssl user to see if it works
    try {
        $dsn = "mysql:host=localhost;port=3306;dbname=hospital";
        $ssl_pdo = new PDO($dsn, 'ssl', '@password..', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "✅ SSL user can already connect to 'hospital' database!\n";
        echo "Database setup is correct. The migration should work.\n";
        
        // Test a simple query
        $stmt = $ssl_pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "📊 Current tables in 'hospital' database: " . count($tables) . "\n";
        foreach ($tables as $table) {
            echo "  - $table\n";
        }
        
        exit(0);
        
    } catch (PDOException $e) {
        echo "❌ SSL user cannot connect: " . $e->getMessage() . "\n";
        echo "\nPlease manually create the database and user:\n";
        echo "1. Connect to MySQL as root\n";
        echo "2. CREATE DATABASE hospital CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
        echo "3. CREATE USER 'ssl'@'localhost' IDENTIFIED BY '@password..';\n";
        echo "4. GRANT ALL PRIVILEGES ON hospital.* TO 'ssl'@'localhost';\n";
        echo "5. FLUSH PRIVILEGES;\n";
        exit(1);
    }
}

// If we have root access, set up the database
try {
    echo "\n📊 Creating database 'hospital'...\n";
    $root_pdo->exec("CREATE DATABASE IF NOT EXISTS hospital CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'hospital' created/verified!\n";
    
    echo "\n👤 Creating/updating user 'ssl'...\n";
    
    // Check if user exists
    $stmt = $root_pdo->prepare("SELECT COUNT(*) FROM mysql.user WHERE User = ? AND Host = ?");
    $stmt->execute(['ssl', 'localhost']);
    $user_exists = $stmt->fetchColumn() > 0;
    
    if ($user_exists) {
        echo "User 'ssl'@'localhost' exists. Updating password...\n";
        $root_pdo->exec("ALTER USER 'ssl'@'localhost' IDENTIFIED BY '@password..'");
    } else {
        echo "Creating user 'ssl'@'localhost'...\n";
        $root_pdo->exec("CREATE USER 'ssl'@'localhost' IDENTIFIED BY '@password..'");
    }
    
    echo "✅ User 'ssl'@'localhost' ready!\n";
    
    echo "\n🔐 Granting privileges...\n";
    $root_pdo->exec("GRANT ALL PRIVILEGES ON hospital.* TO 'ssl'@'localhost'");
    $root_pdo->exec("FLUSH PRIVILEGES");
    echo "✅ Privileges granted!\n";
    
    // Test the ssl connection
    echo "\n🔍 Testing 'ssl' user connection...\n";
    $test_dsn = "mysql:host={$working_connection['host']};port={$working_connection['port']};dbname=hospital";
    $test_pdo = new PDO($test_dsn, 'ssl', '@password..', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ SSL user can connect to 'hospital' database!\n";
    
    // Show existing tables
    $stmt = $test_pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "\n📋 Current tables in 'hospital' database: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "  - $table\n";
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "You can now run: php yii migrate/up --interactive=0\n";
    
} catch (PDOException $e) {
    echo "\n❌ Error setting up database: " . $e->getMessage() . "\n";
    exit(1);
}
?>
