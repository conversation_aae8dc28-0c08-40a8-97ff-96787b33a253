<?php

/** @var \yii\web\View $this */
/** @var string $content */

use hail812\adminlte3\assets\AdminLteAsset;
use hail812\adminlte3\widgets\Menu;
use yii\bootstrap4\Html;
use yii\bootstrap4\Breadcrumbs;
use common\widgets\Alert;

AdminLteAsset::register($this);

$this->registerCssFile('https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback');
$this->registerCssFile('https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css');
?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?> | Hospital Management System</title>
    <?php $this->head() ?>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<?php $this->beginBody() ?>

<div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="<?= \yii\helpers\Url::home() ?>" class="nav-link">Home</a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="fas fa-user"></i>
                    <?= Yii::$app->user->identity->username ?? 'Guest' ?>
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user mr-2"></i> Profile
                    </a>
                    <div class="dropdown-divider"></div>
                    <?= Html::beginForm(['/site/logout'], 'post', ['class' => 'dropdown-item']) ?>
                    <?= Html::submitButton('<i class="fas fa-sign-out-alt mr-2"></i> Logout', ['class' => 'btn btn-link p-0 text-left']) ?>
                    <?= Html::endForm() ?>
                </div>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- Brand Logo -->
        <a href="<?= \yii\helpers\Url::home() ?>" class="brand-link">
            <img src="<?= Yii::getAlias('@web') ?>/img/logo.png" alt="HIMS Logo" class="brand-image img-circle elevation-3" style="opacity: .8" onerror="this.style.display='none'">
            <span class="brand-text font-weight-light">Hospital Management</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar user panel (optional) -->
            <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="image">
                    <img src="<?= Yii::getAlias('@web') ?>/img/user.png" class="img-circle elevation-2" alt="User Image" onerror="this.style.display='none'">
                </div>
                <div class="info">
                    <a href="#" class="d-block"><?= Yii::$app->user->identity->username ?? 'Guest User' ?></a>
                </div>
            </div>

            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <?= Menu::widget([
                    'items' => [
                        [
                            'label' => 'Dashboard',
                            'icon' => 'tachometer-alt',
                            'url' => ['/site/index'],
                        ],
                        ['label' => 'PATIENT MANAGEMENT', 'header' => true],
                        [
                            'label' => 'Patients',
                            'icon' => 'users',
                            'url' => '#',
                            'items' => [
                                ['label' => 'All Patients (DB Required)', 'url' => '#', 'iconStyle' => 'far'],
                                ['label' => 'Add Patient (DB Required)', 'url' => '#', 'iconStyle' => 'far'],
                                ['label' => 'Patient Visits (DB Required)', 'url' => '#', 'iconStyle' => 'far'],
                            ]
                        ],
                        ['label' => 'MEDICAL SERVICES', 'header' => true],
                        [
                            'label' => 'Services',
                            'icon' => 'stethoscope',
                            'url' => '#',
                            'items' => [
                                ['label' => 'Master Services', 'url' => ['/master-services/index'], 'iconStyle' => 'far'],
                                ['label' => 'Branch Services', 'url' => ['/branch-services/index'], 'iconStyle' => 'far'],
                                ['label' => 'Departments', 'url' => ['/departments/index'], 'iconStyle' => 'far'],
                            ]
                        ],
                        ['label' => 'BILLING & PAYMENTS', 'header' => true],
                        [
                            'label' => 'Billing',
                            'icon' => 'file-invoice-dollar',
                            'url' => '#',
                            'items' => [
                                ['label' => 'Patient Bills', 'url' => ['/patient-bills/index'], 'iconStyle' => 'far'],
                                ['label' => 'Payments', 'url' => ['/payments/index'], 'iconStyle' => 'far'],
                                ['label' => 'Payment Modes', 'url' => ['/payment-modes/index'], 'iconStyle' => 'far'],
                            ]
                        ],
                        ['label' => 'PHARMACY', 'header' => true],
                        [
                            'label' => 'Pharmacy',
                            'icon' => 'pills',
                            'url' => '#',
                            'items' => [
                                ['label' => 'Items', 'url' => ['/pharmacy-items/index'], 'iconStyle' => 'far'],
                                ['label' => 'Inventory', 'url' => ['/pharmacy-inventory/index'], 'iconStyle' => 'far'],
                                ['label' => 'Sales', 'url' => ['/pharmacy-sales/index'], 'iconStyle' => 'far'],
                            ]
                        ],
                        ['label' => 'ADMINISTRATION', 'header' => true],
                        [
                            'label' => 'System',
                            'icon' => 'cogs',
                            'url' => '#',
                            'items' => [
                                ['label' => 'Hospitals', 'url' => ['/hospitals/index'], 'iconStyle' => 'far'],
                                ['label' => 'Branches', 'url' => ['/branches/index'], 'iconStyle' => 'far'],
                                ['label' => 'Staff', 'url' => ['/staff/index'], 'iconStyle' => 'far'],
                                ['label' => 'Users', 'url' => ['/users/index'], 'iconStyle' => 'far'],
                                ['label' => 'Roles', 'url' => ['/roles/index'], 'iconStyle' => 'far'],
                            ]
                        ],
                    ]
                ]) ?>
            </nav>
        </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0"><?= Html::encode($this->title) ?></h1>
                    </div>
                    <div class="col-sm-6">
                        <?= Breadcrumbs::widget([
                            'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
                            'options' => ['class' => 'breadcrumb float-sm-right']
                        ]) ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <?= Alert::widget() ?>
                <?= $content ?>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>Copyright &copy; <?= date('Y') ?> <a href="#">Hospital Management System</a>.</strong>
        All rights reserved.
        <div class="float-right d-none d-sm-inline-block">
            <b>Version</b> 1.0.0
        </div>
    </footer>
</div>

<?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>
