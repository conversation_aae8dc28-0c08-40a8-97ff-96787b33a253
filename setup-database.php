<?php
// Setup database script for LAMPP/XAMPP
echo "Setting up database for Hospital Management System...\n";

// Try different connection methods
$connections = [
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3307, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3307, 'user' => 'root', 'pass' => ''],
];

$pdo = null;
$successful_connection = null;

foreach ($connections as $conn) {
    try {
        $dsn = "mysql:host={$conn['host']};port={$conn['port']}";
        echo "Trying connection: {$dsn} with user '{$conn['user']}'...\n";
        
        $pdo = new PDO($dsn, $conn['user'], $conn['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "✅ Successfully connected to MySQL!\n";
        $successful_connection = $conn;
        break;
        
    } catch (PDOException $e) {
        echo "❌ Failed: " . $e->getMessage() . "\n";
        continue;
    }
}

if (!$pdo) {
    echo "❌ Could not establish any MySQL connection. Please check if MySQL is running.\n";
    exit(1);
}

try {
    // Create database if it doesn't exist
    echo "\n📊 Creating database 'hmis'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS hmis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'hmis' created successfully!\n";
    
    // Create user if it doesn't exist
    echo "\n👤 Creating user 'ssl'...\n";
    $pdo->exec("CREATE USER IF NOT EXISTS 'ssl'@'localhost' IDENTIFIED BY '@password..'");
    echo "✅ User 'ssl' created successfully!\n";
    
    // Grant privileges
    echo "\n🔐 Granting privileges...\n";
    $pdo->exec("GRANT ALL PRIVILEGES ON hmis.* TO 'ssl'@'localhost'");
    $pdo->exec("FLUSH PRIVILEGES");
    echo "✅ Privileges granted successfully!\n";
    
    // Test the new connection
    echo "\n🔍 Testing new connection...\n";
    $test_dsn = "mysql:host={$successful_connection['host']};port={$successful_connection['port']};dbname=hmis";
    $test_pdo = new PDO($test_dsn, 'ssl', '@password..', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ Connection test successful!\n";
    
    // Show existing tables
    $stmt = $test_pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "\n📋 Current tables in 'hmis' database: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "  - $table\n";
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "Connection details:\n";
    echo "  Host: {$successful_connection['host']}\n";
    echo "  Port: {$successful_connection['port']}\n";
    echo "  Database: hmis\n";
    echo "  Username: ssl\n";
    echo "  Password: @password..\n";
    
} catch (PDOException $e) {
    echo "❌ Database setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
