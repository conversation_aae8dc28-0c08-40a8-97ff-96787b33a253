<?php
return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'components' => [
        'cache' => [
            'class' => \yii\caching\FileCache::class,
        ],
        'errorHandler' => [
            'class' => \yii\web\ErrorHandler::class,
            // Suppress PHP startup errors related to missing extensions
            'discardExistingOutput' => true,
        ],
    ],
];
