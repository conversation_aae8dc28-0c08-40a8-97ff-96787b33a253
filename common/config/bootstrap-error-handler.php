<?php
/**
 * Custom error handler to suppress PHP extension loading errors
 */

// Set custom error handler to suppress tidy extension errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // Suppress tidy extension errors
    if (strpos($errstr, 'tidy.so') !== false || 
        strpos($errstr, 'libtidy.so') !== false ||
        strpos($errstr, 'Unable to load dynamic library') !== false) {
        return true; // Suppress the error
    }
    
    // Let other errors be handled normally
    return false;
}, E_WARNING | E_NOTICE);

// Set custom exception handler
set_exception_handler(function($exception) {
    // Log the exception but don't display it
    error_log("Uncaught exception: " . $exception->getMessage());
    
    // In production, show a generic error page
    if (!YII_DEBUG) {
        echo "An error occurred. Please try again later.";
        exit;
    }
    
    // In development, show the full exception
    throw $exception;
});

// Suppress specific PHP startup warnings
error_reporting(E_ALL & ~E_WARNING & ~E_NOTICE & ~E_DEPRECATED);
?>
