<?php

namespace frontend\tests\unit\models;

use frontend\models\ContactForm;
use yii\mail\MessageInterface;

class ContactFormTest extends \Codeception\Test\Unit
{
    public function testSendEmail()
    {
        $model = new ContactForm();

        $model->attributes = [
            'name' => 'Tester',
            'email' => '<EMAIL>',
            'subject' => 'very important letter subject',
            'body' => 'body of current message',
        ];

        verify($model->sendEmail('<EMAIL>'))->notEmpty();

        // using Yii2 module actions to check email was sent
        $this->tester->seeEmailIsSent();

        /** @var MessageInterface  $emailMessage */
        $emailMessage = $this->tester->grabLastSentEmail();
        verify($emailMessage)->instanceOf('yii\mail\MessageInterface');
        verify($emailMessage->getTo())->arrayHasKey('<EMAIL>');
        verify($emailMessage->getFrom())->arrayHasKey('<EMAIL>');
        verify($emailMessage->getReplyTo())->arrayHasKey('<EMAIL>');
        verify($emailMessage->getSubject())->equals('very important letter subject');
        verify($emailMessage->toString())->stringContainsString('body of current message');
    }
}
