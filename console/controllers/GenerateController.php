<?php

namespace console\controllers;

use yii\console\Controller;
use yii\gii\generators\model\Generator as ModelGenerator;
use yii\gii\generators\crud\Generator as CrudGenerator;

class GenerateController extends Controller
{
    /**
     * Generate all ActiveRecord models from database tables
     */
    public function actionModels()
    {
        $generator = new ModelGenerator();
        $generator->template = 'default';
        $generator->ns = 'common\models';
        $generator->baseClass = 'yii\db\ActiveRecord';
        $generator->generateRelations = true;
        $generator->generateLabelsFromComments = true;
        $generator->useTablePrefix = false;
        $generator->useSchemaName = false;
        
        // Get all table names from database
        $db = \Yii::$app->db;
        $tables = $db->schema->getTableNames();
        
        $generated = [];
        $errors = [];
        
        foreach ($tables as $table) {
            // Skip views and system tables
            if (strpos($table, '_summary') !== false || 
                strpos($table, '_status') !== false || 
                strpos($table, '_coverage') !== false) {
                continue;
            }
            
            $generator->tableName = $table;
            $generator->modelClass = $this->generateClassName($table);
            
            try {
                $files = $generator->generate();
                if (!empty($files)) {
                    foreach ($files as $file) {
                        $file->save();
                        $generated[] = $generator->modelClass;
                        $this->stdout("Generated model: {$generator->modelClass} for table: {$table}\n");
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "Error generating {$generator->modelClass}: " . $e->getMessage();
                $this->stderr("Error generating {$generator->modelClass}: " . $e->getMessage() . "\n");
            }
        }
        
        $this->stdout("\n=== SUMMARY ===\n");
        $this->stdout("Generated " . count($generated) . " models successfully:\n");
        foreach ($generated as $model) {
            $this->stdout("- $model\n");
        }
        
        if (!empty($errors)) {
            $this->stderr("\nErrors encountered:\n");
            foreach ($errors as $error) {
                $this->stderr("- $error\n");
            }
        }
    }
    
    /**
     * Generate CRUD controllers and views for main entities
     */
    public function actionCrud()
    {
        $mainEntities = [
            'Patients',
            'PatientVisits', 
            'PatientBills',
            'Payments',
            'Staff',
            'Users',
            'Hospitals',
            'Branches',
            'Departments',
            'MasterServices',
            'BranchServices',
            'PharmacyItems',
            'PharmacyInventory',
            'PharmacySales',
            'Roles',
            'PaymentModes'
        ];
        
        $generator = new CrudGenerator();
        $generator->template = 'default';
        $generator->controllerClass = '';
        $generator->modelClass = '';
        $generator->searchModelClass = '';
        $generator->baseControllerClass = 'yii\web\Controller';
        $generator->viewPath = '';
        $generator->enableI18N = false;
        $generator->enablePjax = true;
        
        $generated = [];
        $errors = [];
        
        foreach ($mainEntities as $entity) {
            $generator->modelClass = "common\\models\\$entity";
            $generator->searchModelClass = "backend\\models\\{$entity}Search";
            $generator->controllerClass = "backend\\controllers\\{$entity}Controller";
            $generator->viewPath = "@backend/views/" . strtolower(preg_replace('/(?<!^)[A-Z]/', '-$0', $entity));
            
            try {
                $files = $generator->generate();
                if (!empty($files)) {
                    foreach ($files as $file) {
                        $file->save();
                    }
                    $generated[] = $entity;
                    $this->stdout("Generated CRUD for: $entity\n");
                }
            } catch (\Exception $e) {
                $errors[] = "Error generating CRUD for $entity: " . $e->getMessage();
                $this->stderr("Error generating CRUD for $entity: " . $e->getMessage() . "\n");
            }
        }
        
        $this->stdout("\n=== CRUD SUMMARY ===\n");
        $this->stdout("Generated CRUD for " . count($generated) . " entities:\n");
        foreach ($generated as $entity) {
            $this->stdout("- $entity\n");
        }
        
        if (!empty($errors)) {
            $this->stderr("\nErrors encountered:\n");
            foreach ($errors as $error) {
                $this->stderr("- $error\n");
            }
        }
    }
    
    /**
     * Generate class name from table name
     */
    private function generateClassName($tableName)
    {
        // Convert snake_case to PascalCase
        $parts = explode('_', $tableName);
        $className = '';
        foreach ($parts as $part) {
            $className .= ucfirst($part);
        }
        return $className;
    }
}
