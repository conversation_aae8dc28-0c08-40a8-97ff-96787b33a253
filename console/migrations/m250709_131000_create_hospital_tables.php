<?php

use yii\db\Migration;

/**
 * Class m250709_131000_create_hospital_tables
 * Creates the core hospital management tables
 */
class m250709_131000_create_hospital_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        // Hospitals table
        $this->createTable('{{%hospitals}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(255)->notNull(),
            'code' => $this->string(50)->notNull()->unique(),
            'address' => $this->text(),
            'phone' => $this->string(20),
            'email' => $this->string(100),
            'website' => $this->string(255),
            'license_number' => $this->string(100),
            'established_date' => $this->date(),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Branches table
        $this->createTable('{{%branches}}', [
            'id' => $this->primaryKey(),
            'hospital_id' => $this->integer()->notNull(),
            'name' => $this->string(255)->notNull(),
            'code' => $this->string(50)->notNull(),
            'address' => $this->text(),
            'phone' => $this->string(20),
            'email' => $this->string(100),
            'manager_name' => $this->string(255),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Departments table
        $this->createTable('{{%departments}}', [
            'id' => $this->primaryKey(),
            'branch_id' => $this->integer()->notNull(),
            'name' => $this->string(255)->notNull(),
            'code' => $this->string(50)->notNull(),
            'description' => $this->text(),
            'head_of_department' => $this->string(255),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Roles table
        $this->createTable('{{%roles}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(100)->notNull()->unique(),
            'description' => $this->text(),
            'permissions' => $this->json(),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Staff table
        $this->createTable('{{%staff}}', [
            'id' => $this->primaryKey(),
            'employee_id' => $this->string(50)->notNull()->unique(),
            'user_id' => $this->integer(),
            'branch_id' => $this->integer()->notNull(),
            'department_id' => $this->integer(),
            'role_id' => $this->integer()->notNull(),
            'first_name' => $this->string(100)->notNull(),
            'last_name' => $this->string(100)->notNull(),
            'middle_name' => $this->string(100),
            'gender' => $this->string(10),
            'date_of_birth' => $this->date(),
            'phone' => $this->string(20),
            'email' => $this->string(100),
            'address' => $this->text(),
            'qualification' => $this->string(255),
            'specialization' => $this->string(255),
            'license_number' => $this->string(100),
            'hire_date' => $this->date()->notNull(),
            'salary' => $this->decimal(10, 2),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Patients table
        $this->createTable('{{%patients}}', [
            'id' => $this->primaryKey(),
            'patient_id' => $this->string(50)->notNull()->unique(),
            'first_name' => $this->string(100)->notNull(),
            'last_name' => $this->string(100)->notNull(),
            'middle_name' => $this->string(100),
            'gender' => $this->string(10)->notNull(),
            'date_of_birth' => $this->date()->notNull(),
            'phone' => $this->string(20),
            'email' => $this->string(100),
            'address' => $this->text(),
            'emergency_contact_name' => $this->string(255),
            'emergency_contact_phone' => $this->string(20),
            'blood_group' => $this->string(10),
            'allergies' => $this->text(),
            'medical_history' => $this->text(),
            'insurance_number' => $this->string(100),
            'insurance_provider' => $this->string(255),
            'status' => $this->smallInteger()->notNull()->defaultValue(1),
            'created_at' => $this->integer()->notNull(),
            'updated_at' => $this->integer()->notNull(),
        ], $tableOptions);

        // Add foreign keys
        $this->addForeignKey(
            'fk-branches-hospital_id',
            '{{%branches}}',
            'hospital_id',
            '{{%hospitals}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-departments-branch_id',
            '{{%departments}}',
            'branch_id',
            '{{%branches}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-staff-user_id',
            '{{%staff}}',
            'user_id',
            '{{%user}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'fk-staff-branch_id',
            '{{%staff}}',
            'branch_id',
            '{{%branches}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-staff-department_id',
            '{{%staff}}',
            'department_id',
            '{{%departments}}',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'fk-staff-role_id',
            '{{%staff}}',
            'role_id',
            '{{%roles}}',
            'id',
            'CASCADE'
        );

        // Create indexes
        $this->createIndex('idx-hospitals-code', '{{%hospitals}}', 'code');
        $this->createIndex('idx-branches-code', '{{%branches}}', 'code');
        $this->createIndex('idx-departments-code', '{{%departments}}', 'code');
        $this->createIndex('idx-staff-employee_id', '{{%staff}}', 'employee_id');
        $this->createIndex('idx-patients-patient_id', '{{%patients}}', 'patient_id');
        $this->createIndex('idx-patients-phone', '{{%patients}}', 'phone');
        $this->createIndex('idx-patients-email', '{{%patients}}', 'email');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%patients}}');
        $this->dropTable('{{%staff}}');
        $this->dropTable('{{%roles}}');
        $this->dropTable('{{%departments}}');
        $this->dropTable('{{%branches}}');
        $this->dropTable('{{%hospitals}}');
    }
}
