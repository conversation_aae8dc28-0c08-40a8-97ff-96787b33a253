<?php
// Test database connection without specifying database
echo "Testing MySQL connection without database...\n";

try {
    $dsn = 'mysql:host=127.0.0.1;port=3306';
    $username = 'ssl';
    $password = '@password..';
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ Connection successful!\n";
    
    // Check if hmis database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'hmis'");
    $db_exists = $stmt->fetch();
    
    if ($db_exists) {
        echo "✅ Database 'hmis' exists!\n";
    } else {
        echo "❌ Database 'hmis' does not exist. Creating it...\n";
        $pdo->exec("CREATE DATABASE hmis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database 'hmis' created successfully!\n";
    }
    
    // Now test connection to the hmis database
    $dsn_with_db = 'mysql:host=127.0.0.1;port=3306;dbname=hmis';
    $pdo_with_db = new PDO($dsn_with_db, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ Connection to 'hmis' database successful!\n";
    
    // Show tables
    $stmt = $pdo_with_db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📊 Found " . count($tables) . " tables in the 'hmis' database:\n";
    foreach ($tables as $table) {
        echo "  - $table\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
}
?>
