<?php
// <PERSON>ript to fix database connection issues
echo "=== Database Connection Troubleshooting ===\n";

// Test different connection methods
$connections = [
    // Try socket connection (common in LAMPP)
    ['dsn' => 'mysql:unix_socket=/opt/lampp/var/mysql/mysql.sock', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:unix_socket=/var/run/mysqld/mysqld.sock', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:unix_socket=/tmp/mysql.sock', 'user' => 'root', 'pass' => ''],
    // Try different hosts and ports
    ['dsn' => 'mysql:host=localhost;port=3306', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:host=127.0.0.1;port=3306', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:host=localhost;port=3307', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:host=127.0.0.1;port=3307', 'user' => 'root', 'pass' => ''],
    ['dsn' => 'mysql:host=localhost;port=3306', 'user' => 'root', 'pass' => 'root'],
    ['dsn' => 'mysql:host=localhost;port=3306', 'user' => 'ssl', 'pass' => '@password..'],
];

$working_connection = null;

foreach ($connections as $i => $conn) {
    echo "\n" . ($i + 1) . ". Testing: {$conn['dsn']} with user '{$conn['user']}'" .
         ($conn['pass'] ? " (with password)" : " (no password)") . "\n";

    try {
        $pdo = new PDO($conn['dsn'], $conn['user'], $conn['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5,
        ]);
        
        echo "   ✅ Connection successful!\n";
        
        // Check databases
        $stmt = $pdo->query("SHOW DATABASES");
        $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "   📊 Available databases: " . implode(', ', $databases) . "\n";
        
        $working_connection = $conn;
        $working_connection['databases'] = $databases;
        break;
        
    } catch (PDOException $e) {
        echo "   ❌ Failed: " . $e->getMessage() . "\n";
    }
}

if (!$working_connection) {
    echo "\n❌ No working connection found. Please check:\n";
    echo "1. MySQL service is running\n";
    echo "2. Correct host and port\n";
    echo "3. Valid username and password\n";
    exit(1);
}

echo "\n=== Working Connection Found ===\n";
echo "DSN: {$working_connection['dsn']}\n";
echo "User: {$working_connection['user']}\n";

// Now try to create the required database and user
try {
    $pdo = new PDO($working_connection['dsn'], $working_connection['user'], $working_connection['pass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    // Create database if it doesn't exist
    $target_db = 'hospital';
    if (!in_array($target_db, $working_connection['databases'])) {
        echo "\n📊 Creating database '$target_db'...\n";
        $pdo->exec("CREATE DATABASE `$target_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database '$target_db' created successfully!\n";
    } else {
        echo "\n✅ Database '$target_db' already exists!\n";
    }
    
    // Check if ssl user exists
    echo "\n👤 Checking user 'ssl'...\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM mysql.user WHERE User = ? AND Host = ?");
    $stmt->execute(['ssl', 'localhost']);
    $user_exists = $stmt->fetchColumn() > 0;
    
    if (!$user_exists) {
        echo "Creating user 'ssl'@'localhost'...\n";
        $pdo->exec("CREATE USER 'ssl'@'localhost' IDENTIFIED BY '@password..'");
        echo "✅ User created!\n";
    } else {
        echo "✅ User 'ssl'@'localhost' already exists!\n";
        
        // Try to update password in case it's wrong
        echo "Updating password for user 'ssl'@'localhost'...\n";
        $pdo->exec("ALTER USER 'ssl'@'localhost' IDENTIFIED BY '@password..'");
        echo "✅ Password updated!\n";
    }
    
    // Grant privileges
    echo "\n🔐 Granting privileges...\n";
    $pdo->exec("GRANT ALL PRIVILEGES ON `$target_db`.* TO 'ssl'@'localhost'");
    $pdo->exec("FLUSH PRIVILEGES");
    echo "✅ Privileges granted!\n";
    
    // Test the ssl connection
    echo "\n🔍 Testing 'ssl' user connection...\n";
    // Build test DSN based on working connection
    if (strpos($working_connection['dsn'], 'unix_socket') !== false) {
        $test_dsn = $working_connection['dsn'] . ";dbname=$target_db";
    } else {
        $test_dsn = $working_connection['dsn'] . ";dbname=$target_db";
    }
    $test_pdo = new PDO($test_dsn, 'ssl', '@password..', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    echo "✅ SSL user can connect to '$target_db' database!\n";
    
    // Update the Yii2 configuration
    echo "\n⚙️ Updating Yii2 configuration...\n";
    $config_content = "<?php

return [
    'components' => [
        'db' => [
            'class' => \\yii\\db\\Connection::class,
            'dsn' => '{$working_connection['dsn']};dbname=$target_db',
            'username' => 'ssl',
            'password' => '@password..',
            'charset' => 'utf8mb4',
        ],
        'mailer' => [
            'class' => \\yii\\symfonymailer\\Mailer::class,
            'viewPath' => '@common/mail',
            'useFileTransport' => true,
        ],
    ],
];
";
    
    file_put_contents('common/config/main-local.php', $config_content);
    echo "✅ Configuration updated!\n";
    
    echo "\n🎉 Database connection fixed successfully!\n";
    echo "You can now run: php yii migrate/up --interactive=0\n";
    
} catch (PDOException $e) {
    echo "\n❌ Error setting up database: " . $e->getMessage() . "\n";
}
?>
